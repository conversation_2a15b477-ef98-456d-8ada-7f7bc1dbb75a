import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface MediaSettings {
  defaultOutputDir: string
  maxConcurrentTasks: number
  autoStartBatch: boolean
  enableNotifications: boolean
  autoCleanupCompleted: boolean
  cleanupAfterHours: number
  preserveOriginalFiles: boolean
}

/**
 * 媒体设置管理 Store
 * 负责应用设置的管理和持久化
 */
export const useMediaSettingsStore = defineStore('media-settings', () => {
  // 设置状态
  const settings = ref<MediaSettings>({
    defaultOutputDir: '~/Downloads/xhs-media-output',
    maxConcurrentTasks: 3,
    autoStartBatch: false,
    enableNotifications: true,
    autoCleanupCompleted: true,
    cleanupAfterHours: 24,
    preserveOriginalFiles: true
  })

  const isLoaded = ref(false)

  // 加载设置
  const loadSettings = async (): Promise<void> => {
    if (isLoaded.value) return

    try {
      const response = await window.electronAPI.media.getSettings()
      if (response.success && response.data) {
        Object.assign(settings.value, response.data)
        console.log('[SettingsStore] 设置加载成功')
      }
    } catch (error) {
      console.warn('[SettingsStore] 加载设置失败，使用默认设置:', error)
    } finally {
      isLoaded.value = true
    }
  }

  // 保存设置
  const saveSettings = async (): Promise<void> => {
    try {
      const response = await window.electronAPI.media.saveSettings(settings.value)
      if (!response.success) {
        throw new Error(response.error)
      }
      console.log('[SettingsStore] 设置保存成功')
    } catch (error) {
      console.error('[SettingsStore] 保存设置失败:', error)
      throw error
    }
  }

  // 更新设置
  const updateSettings = async (newSettings: Partial<MediaSettings>): Promise<void> => {
    Object.assign(settings.value, newSettings)
    await saveSettings()
  }

  // 重置为默认设置
  const resetToDefaults = async (): Promise<void> => {
    settings.value = {
      defaultOutputDir: '~/Downloads/xhs-media-output',
      maxConcurrentTasks: 3,
      autoStartBatch: false,
      enableNotifications: true,
      autoCleanupCompleted: true,
      cleanupAfterHours: 24,
      preserveOriginalFiles: true
    }
    await saveSettings()
  }

  return {
    // 状态
    settings,
    isLoaded,

    // 方法
    loadSettings,
    saveSettings,
    updateSettings,
    resetToDefaults
  }
})
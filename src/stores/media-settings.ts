import { defineStore } from 'pinia'
import { reactive, computed } from 'vue'
import { useMediaEvents } from '@/composables/useMediaEvents'

export interface MediaSettings {
  defaultOutputDir: string
  outputDirectories: Map<string, string> // 按任务类型存储的输出目录
  maxConcurrentTasks: number
  autoStartBatch: boolean
  enableNotifications: boolean
  autoCleanupCompleted: boolean
  cleanupAfterHours: number
  preserveOriginalFiles: boolean
  ffmpegPath?: string
  tempDirectory: string
  defaultVideoFormat: string
  defaultAudioFormat: string
  defaultImageFormat: string
  jpegQuality: number
  pngCompression: number
  videoQualityPreset: 'low' | 'medium' | 'high' | 'custom'
  customVideoBitrate?: string
  customAudioBitrate?: string
}

export interface PresetOptions {
  video: {
    [preset: string]: {
      format: string
      quality: string
      codec?: string
      bitrate?: string
    }
  }
  audio: {
    [preset: string]: {
      format: string
      quality: string
      bitrate?: string
    }
  }
  image: {
    [preset: string]: {
      format: string
      quality: number
      optimize: boolean
    }
  }
}

/**
 * 媒体设置管理 Store
 * 负责应用设置的管理和持久化
 */
export const useMediaSettingsStore = defineStore('media-settings', () => {
  const { mediaEventBus } = useMediaEvents()
  
  // 状态定义 - 明确区分持久化和非持久化
  const state = reactive({
    // 持久化数据
    persistent: {
      settings: {
        defaultOutputDir: '~/Downloads/xhs-media-output',
        outputDirectories: new Map<string, string>(),
        maxConcurrentTasks: 3,
        autoStartBatch: false,
        enableNotifications: true,
        autoCleanupCompleted: true,
        cleanupAfterHours: 24,
        preserveOriginalFiles: true,
        tempDirectory: '~/Downloads/xhs-media-temp',
        defaultVideoFormat: 'mp4',
        defaultAudioFormat: 'mp3',
        defaultImageFormat: 'webp',
        jpegQuality: 85,
        pngCompression: 9,
        videoQualityPreset: 'medium' as const,
        customVideoBitrate: '2000k',
        customAudioBitrate: '192k'
      } as MediaSettings,
      
      presets: {
        video: {
          'low': { format: 'mp4', quality: '1000k', codec: 'h264' },
          'medium': { format: 'mp4', quality: '2000k', codec: 'h264' },
          'high': { format: 'mp4', quality: '4000k', codec: 'h264' },
          'web': { format: 'webm', quality: '1500k', codec: 'vp9' },
          'mobile': { format: 'mp4', quality: '800k', codec: 'h264' }
        },
        audio: {
          'low': { format: 'mp3', quality: '128k' },
          'medium': { format: 'mp3', quality: '192k' },
          'high': { format: 'mp3', quality: '320k' },
          'lossless': { format: 'flac', quality: 'best' }
        },
        image: {
          'web': { format: 'webp', quality: 85, optimize: true },
          'print': { format: 'png', quality: 100, optimize: false },
          'thumbnail': { format: 'jpeg', quality: 70, optimize: true }
        }
      } as PresetOptions,
      
      // 用户自定义预设
      customPresets: new Map<string, any>()
    },
    
    // 会话数据（不持久化）
    session: {
      isLoaded: false,
      isSaving: false,
      lastSaveTime: 0
    }
  })
  
  // 计算属性
  const currentVideoPreset = computed(() => {
    const preset = state.persistent.settings.videoQualityPreset
    return preset === 'custom' 
      ? { format: state.persistent.settings.defaultVideoFormat, quality: state.persistent.settings.customVideoBitrate }
      : state.persistent.presets.video[preset]
  })
  
  const currentAudioPreset = computed(() => {
    return state.persistent.presets.audio['medium'] // 默认中等质量
  })
  
  const currentImagePreset = computed(() => {
    return state.persistent.presets.image['web'] // 默认web优化
  })
  
  // 获取特定任务类型的输出目录
  const getOutputDirectory = (taskType: string): string => {
    return state.persistent.settings.outputDirectories.get(taskType) || 
           state.persistent.settings.defaultOutputDir
  }
  
  // 设置特定任务类型的输出目录
  const setOutputDirectory = (taskType: string, directory: string) => {
    state.persistent.settings.outputDirectories.set(taskType, directory)
    // 触发持久化
    saveToStorage()
  }
  
  // 更新设置
  const updateSettings = (newSettings: Partial<MediaSettings>) => {
    Object.assign(state.persistent.settings, newSettings)
    // 触发持久化
    saveToStorage()
    // 发送设置更新事件
    mediaEventBus.emit('settings:update', undefined)
  }
  
  // 添加自定义预设
  const addCustomPreset = (type: 'video' | 'audio' | 'image', name: string, preset: any) => {
    const key = `${type}:${name}`
    state.persistent.customPresets.set(key, preset)
    saveToStorage()
  }
  
  // 删除自定义预设
  const removeCustomPreset = (type: 'video' | 'audio' | 'image', name: string) => {
    const key = `${type}:${name}`
    state.persistent.customPresets.delete(key)
    saveToStorage()
  }
  
  // 获取所有预设（包括自定义）
  const getAllPresets = (type: 'video' | 'audio' | 'image') => {
    const builtInPresets = state.persistent.presets[type]
    const customPresets: any = {}
    
    state.persistent.customPresets.forEach((preset, key) => {
      if (key.startsWith(`${type}:`)) {
        const name = key.substring(type.length + 1)
        customPresets[name] = preset
      }
    })
    
    return { ...builtInPresets, ...customPresets }
  }
  
  // 重置为默认设置
  const resetToDefaults = () => {
    state.persistent.settings = {
      defaultOutputDir: '~/Downloads/xhs-media-output',
      outputDirectories: new Map<string, string>(),
      maxConcurrentTasks: 3,
      autoStartBatch: false,
      enableNotifications: true,
      autoCleanupCompleted: true,
      cleanupAfterHours: 24,
      preserveOriginalFiles: true,
      tempDirectory: '~/Downloads/xhs-media-temp',
      defaultVideoFormat: 'mp4',
      defaultAudioFormat: 'mp3',
      defaultImageFormat: 'webp',
      jpegQuality: 85,
      pngCompression: 9,
      videoQualityPreset: 'medium',
      customVideoBitrate: '2000k',
      customAudioBitrate: '192k'
    }
    state.persistent.customPresets.clear()
    saveToStorage()
  }
  
  // 持久化到存储
  let saveTimer: NodeJS.Timeout
  const saveToStorage = () => {
    if (!window.electronAPI) return
    
    clearTimeout(saveTimer)
    saveTimer = setTimeout(async () => {
      state.session.isSaving = true
      try {
        // 转换 Map 为普通对象以便序列化
        const dataToSave = {
          ...state.persistent,
          settings: {
            ...state.persistent.settings,
            outputDirectories: Object.fromEntries(state.persistent.settings.outputDirectories)
          },
          customPresets: Object.fromEntries(state.persistent.customPresets)
        }
        
        await window.electronAPI.storage.set('media-settings', 'data', dataToSave)
        state.session.lastSaveTime = Date.now()
      } catch (error) {
        console.error('[MediaSettings] 保存设置失败:', error)
      } finally {
        state.session.isSaving = false
      }
    }, 1000) // 1秒防抖
  }
  
  // 从存储加载
  const loadFromStorage = async () => {
    if (!window.electronAPI || state.session.isLoaded) return
    
    try {
      const response = await window.electronAPI.storage.get('media-settings', 'data')
      if (response.success && response.data) {
        const data = response.data
        
        // 恢复设置
        if (data.settings) {
          Object.assign(state.persistent.settings, data.settings)
          // 恢复 Map 类型
          if (data.settings.outputDirectories) {
            state.persistent.settings.outputDirectories = new Map(Object.entries(data.settings.outputDirectories))
          }
        }
        
        // 恢复预设
        if (data.presets) {
          Object.assign(state.persistent.presets, data.presets)
        }
        
        // 恢复自定义预设
        if (data.customPresets) {
          state.persistent.customPresets = new Map(Object.entries(data.customPresets))
        }
      }
    } catch (error) {
      console.error('[MediaSettings] 加载设置失败:', error)
    } finally {
      state.session.isLoaded = true
    }
  }
  
  // 初始化时加载
  loadFromStorage()
  
  return {
    // 持久化状态
    settings: computed(() => state.persistent.settings),
    presets: computed(() => state.persistent.presets),
    customPresets: computed(() => state.persistent.customPresets),
    
    // 会话状态
    isLoaded: computed(() => state.session.isLoaded),
    isSaving: computed(() => state.session.isSaving),
    
    // 计算属性
    currentVideoPreset,
    currentAudioPreset,
    currentImagePreset,
    
    // 方法
    getOutputDirectory,
    setOutputDirectory,
    updateSettings,
    addCustomPreset,
    removeCustomPreset,
    getAllPresets,
    resetToDefaults,
    loadFromStorage,
    outputDirectories: computed(() => state.persistent.settings.outputDirectories)
  }
})
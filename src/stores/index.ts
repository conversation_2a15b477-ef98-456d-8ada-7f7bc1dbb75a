import { createPinia } from 'pinia'

// 创建 Pinia 实例
export const pinia = createPinia()

// 简化持久化方案 - 每个 store 自己管理持久化
console.log('[PiniaIndex] 使用简化的持久化方案')


// 导出 store 创建函数（延迟加载）
export { useMediaTasksStore } from './media-tasks-new'
export { useMediaSettingsStore } from './media-settings-new'
export { useMediaMainStore } from './media-main-new'
export { useMediaStatsStore } from './media-stats'

// 导出类型
export type { MediaSettings } from './media-settings-new'
export type { ProcessingOptions, SingleTask, TaskResult } from './media-tasks-new'
export type { ProcessingStats } from './media-stats'
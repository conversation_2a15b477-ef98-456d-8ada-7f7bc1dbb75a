<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside width="220px" class="app-sidebar">
        <div class="sidebar-header">
          <img src="@/assets/logo/logo-min.png" alt="小红书工具箱" class="logo" />
          <h2 class="app-title">小红书工具箱</h2>
        </div>

        <el-menu :router="true" :default-active="activeRoute" class="sidebar-menu" background-color="#fff"
          text-color="#606266" active-text-color="#ff2442">

          <el-menu-item index="/">
            <el-icon>
              <HomeFilled />
            </el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-menu-item index="/note">
            <el-icon>
              <Document />
            </el-icon>
            <span>笔记工具</span>
          </el-menu-item>

          <!-- <el-menu-item index="/check">
						<el-icon><Search /></el-icon>
						<span>检测工具</span>
					</el-menu-item> -->

          <!-- <el-menu-item index="/user_check">
						<el-icon><User /></el-icon>
						<span>用户检测</span>
					</el-menu-item> -->

          <el-menu-item index="/batch">
            <el-icon>
              <Operation />
            </el-icon>
            <span>批量处理</span>
          </el-menu-item>

          <el-menu-item index="/monitor">
            <el-icon>
              <TrendCharts />
            </el-icon>
            <span>数据监控</span>
          </el-menu-item>

          <el-menu-item index="/media">
            <el-icon>
              <VideoCamera />
            </el-icon>
            <span>多媒体处理</span>
          </el-menu-item>

          <el-menu-item index="/media-demo">
            <el-icon>
              <VideoCamera />
            </el-icon>
            <span>新多媒体Demo</span>
          </el-menu-item>
          
          <!-- <el-menu-item index="/account">
						<el-icon><Setting /></el-icon>
						<span>账号管理</span>
					</el-menu-item> -->
        </el-menu>

        <div class="sidebar-footer">
          <span class="version-info">v{{ appVersion }}</span>
          <el-button link class="check-update-btn" @click="checkForUpdate">检查更新</el-button>
        </div>
      </el-aside>

      <!-- 主内容区域 -->
      <el-container class="main-container">
        <el-header height="60px" class="app-header">
          <div class="header-left">
            <h3 class="page-name">{{ pageName }}</h3>
          </div>
          <div class="header-right">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-profile">
                <!-- 如果有头像就显示头像，没有头像显示 UserFilled avatar-->
                <el-avatar class="avatar-img" v-if="loginInfo?.images" :size="36" :src="loginInfo.images"></el-avatar>
                <el-icon v-else class="avatar">
                  <UserFilled />
                </el-icon>
                <span class="username">{{ loginStatus }}</span>
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="login">登录/切换账号</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <el-main class="app-main">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
          <ProgressBar :show="isDownloadingUpdate" :progress="updateDownloadProgress"
            :receivedBytes="downloadReceivedBytes" :totalBytes="downloadTotalBytes" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'; // Added onUnmounted
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { HomeFilled, Document, Search, User, Operation, Setting, ArrowDown, UserFilled, TrendCharts, DataAnalysis, VideoCamera } from '@element-plus/icons-vue';
import ProgressBar from '@/components/ProgressBar.vue';

const route = useRoute();
const router = useRouter();

const appVersion = ref<string>(__APP_VERSION__);
const loginStatus = ref('未登录');
const loginInfo = ref<any>(null);
const isCheckingUpdate = ref(false);
const updateDownloadProgress = ref(0); // Added for download progress
const isDownloadingUpdate = ref(false); // Added for download status
const downloadReceivedBytes = ref(0); // Store received bytes for progress bar
const downloadTotalBytes = ref(0); // Store total bytes for progress bar
let downloadedFilePath: string | null = null; // Store downloaded file path for installation
const isUpdateDialogShowing = ref(false); // Add state to track if update dialog is showing

// Store unsubscribe functions for IPC listeners
const unsubscribeLoginStatusChanged: (() => void)[] = [];
const unsubscribeUpdateEvents: (() => void)[] = [];


const activeRoute = computed(() => {
  return route.path;
});

const pageName = computed(() => {
  const routeMap: { [key: string]: string } = {
    '/': '首页',
    '/note': '笔记工具',
    '/check': '检测工具',
    '/user_check': '用户检测',
    '/batch': '批量处理',
    '/account': '账号管理',
    '/monitor': '数据监控',
    '/media': '多媒体处理',
    '/media-demo': '新多媒体Demo'
  };
  return routeMap[route.path] || '未知页面';
});

onMounted(() => {
  window.electronAPI.app.login()

  // Listen for login status changes from main process
  unsubscribeLoginStatusChanged.push(window.electronAPI.app.onLoginStatusChanged(() => {
    checkLoginStatus();
  }));

  // Listen for update events from main process
  unsubscribeUpdateEvents.push(window.electronAPI.updater.onCheckingForUpdate(() => {
    console.log('Renderer: Checking for update...');
    isCheckingUpdate.value = true;
    ElMessage.info('正在检查更新...');
  }));

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onUpdateAvailable((info: any, isManual: boolean) => { // Receive isManual
    console.log('Renderer: Update available:', info, 'isManual:', isManual);
    isCheckingUpdate.value = false;

    // Prevent showing multiple dialogs
    if (isUpdateDialogShowing.value) {
      console.log('Renderer: Update dialog already showing, ignoring event.');
      return;
    }

    isUpdateDialogShowing.value = true; // Set flag before showing dialog

    ElMessageBox.confirm(`有可用更新，版本为: ${info.version}，是否立即下载？`, '更新提示', {
      confirmButtonText: '下载',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      isDownloadingUpdate.value = true; // Set to true BEFORE calling IPC
      window.electronAPI.updater.downloadUpdate(info); // Call preload function to trigger download
      updateDownloadProgress.value = 0;
      downloadReceivedBytes.value = 0; // Reset bytes
      downloadTotalBytes.value = 0; // Reset bytes
      ElNotification({
        title: '更新提示',
        message: '开始下载更新...',
        type: 'info',
      });
      isUpdateDialogShowing.value = false; // Reset flag after user interaction
    }).catch(() => {
      ElMessage.info('已取消下载更新');
      isUpdateDialogShowing.value = false; // Reset flag after user interaction
    });
  }));

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onUpdateNotAvailable((isManual: boolean) => { // Receive isManual
    console.log('Renderer: No update available.', 'isManual:', isManual);
    isCheckingUpdate.value = false;
    // Only show message if it was a manual check
    if (isManual) {
      ElMessage.success('您当前已是最新版本。');
    }
  }));

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onUpdateError((error: string, isManual: boolean) => { // Receive isManual
    console.error('Renderer: Update error:', error, 'isManual:', isManual);
    isCheckingUpdate.value = false;
    isDownloadingUpdate.value = false;
    // Only show error message if it was a manual check or during download
    if (isManual || isDownloadingUpdate.value) {
      ElNotification({
        title: '更新错误',
        message: `更新过程中发生错误: ${error}`,
        type: 'error',
      });
    }
  }));

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onDownloadProgress((progressObj: { progress: number; receivedBytes: number; totalBytes: number }) => { // Use correct type
    updateDownloadProgress.value = progressObj.progress;
    downloadReceivedBytes.value = progressObj.receivedBytes; // Store received bytes
    downloadTotalBytes.value = progressObj.totalBytes; // Store total bytes
  }));

  // Removed onUpdateDownloaded as we are using custom download completed event

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onDownloadCompleted((filePath: string) => {
    console.log('Renderer: Download completed:', filePath);
    isDownloadingUpdate.value = false;
    updateDownloadProgress.value = 100; // Ensure progress is 100%
    downloadedFilePath = filePath; // Store file path
    // Note: isUpdateDialogShowing is for the "Update Available" dialog, not this one.
    ElMessageBox.confirm('更新已下载完成，是否立即安装？', '安装更新', {
      confirmButtonText: '安装',
      cancelButtonText: '稍后',
      type: 'info'
    }).then(() => {
      if (downloadedFilePath) {
        window.electronAPI.updater.quitAndInstall(downloadedFilePath); // Call with file path
      } else {
        console.error('Downloaded file path is missing.');
        ElMessage.error('安装文件路径丢失，请重新下载。');
      }
    }).catch(() => {
      ElMessage.info('更新将在下次启动时安装');
    });
  }));

  unsubscribeUpdateEvents.push(window.electronAPI.updater.onDownloadError((error: string) => {
    console.error('Renderer: Download error:', error);
    isDownloadingUpdate.value = false;
    ElNotification({
      title: '下载错误',
      message: `下载更新文件时发生错误: ${error}`,
      type: 'error',
    });
  }));

  // Add listener for install errors
  unsubscribeUpdateEvents.push(window.electronAPI.updater.onInstallError((error: string) => {
    console.error('Renderer: Install error:', error);
    ElNotification({
      title: '安装错误',
      message: `安装更新文件时发生错误: ${error}`,
      type: 'error',
    });
  }));
});

onUnmounted(() => {
  // Clean up IPC listeners
  unsubscribeLoginStatusChanged.forEach(unsubscribe => unsubscribe());
  unsubscribeUpdateEvents.forEach(unsubscribe => unsubscribe());
});


const checkLoginStatus = async () => {
  try {
    const info = await window.electronAPI.app.getLoginInfo(); // Use exposed API
    loginInfo.value = info;
    console.log('Login Info:', info);


    if (info && !info.guest) {
      loginStatus.value = info.nickname || '已登录';
    } else {
      loginStatus.value = '未登录';
    }
  } catch (error) {
    console.error('获取登录状态失败', error);
    loginStatus.value = '未登录';
    loginInfo.value = null; // Ensure loginInfo is null on error
  }
};

const handleCommand = (command: string): void => {
  if (command === 'login') {
    window.electronAPI.app.showLoginWindow(); // Use exposed API
  } else if (command === 'logout') {
    window.electronAPI.app.logout(); // Use exposed API
    // State will be updated by the 'login-status-changed' listener
  }
};

const checkForUpdate = (): void => {
  // 如果正在检查更新或正在下载，则不再发送新的请求
  if (isCheckingUpdate.value || isDownloadingUpdate.value) {
    return;
  }

  // 发送检查更新请求到主进程 (手动检查)
  window.electronAPI.updater.checkForUpdates(true); // Use exposed API
};
</script>

<style lang="scss">
@use '@/assets/styles/index.scss' as *;

#app {
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.app-container {
  height: 100%;
}

.app-sidebar {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  border-right: 1px solid $border-lighter;
  background-color: $background-card;

  .sidebar-header {
    padding: $spacing-base;
    display: flex;
    align-items: center;
    border-bottom: 1px solid $border-lighter;

    .logo {
      width: 48px;
      height: 48px;
      margin-right: $spacing-small;
    }

    .app-title {
      font-size: 18px;
      font-weight: 500;
      color: $text-primary;
      margin: 0;
    }
  }

  .sidebar-menu {
    flex: 1;
    border-right: none;

    .el-menu-item {
      height: 50px;
      line-height: 50px;

      .el-icon {
        margin-right: $spacing-small;
      }

      &.is-active {
        background-color: rgba($primary-color, 0.1);
        border-right: 3px solid $primary-color;
      }
    }
  }

  .sidebar-footer {
    padding: $spacing-base;
    text-align: center;
    color: $text-secondary;
    font-size: 12px;
    border-top: 1px solid $border-lighter;

    .version-info {
      margin-right: $spacing-small;
    }

    .check-update-btn {
      color: $primary-color;
      font-size: 12px;
      padding: 0;

      &:focus,
      &:focus-visible {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

.app-header {
  background-color: $background-card;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid $border-lighter;

  .page-name {
    font-size: 18px;
    font-weight: 500;
    color: $text-primary;
    margin: 0;
  }

  .user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;

    // 头像图片
    .avatar-img {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-right: $spacing-small;
    }

    .avatar {
      font-size: 20px;
      color: $primary-color;
      background-color: rgba($primary-color, 0.1);
      padding: 8px;
      border-radius: 50%;
      margin-right: $spacing-small;
    }

    .username {
      font-size: 14px;
      color: $text-regular;
      margin-right: $spacing-small;
    }
  }
}

.app-main {
  background-color: $background-base;
  padding: $spacing-base;
  overflow-y: auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
// Corrected from .fade-enter
.fade-leave-to {
  opacity: 0;
}

/* 全局去除Element Plus按钮的焦点边框 */
.el-button:focus,
.el-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}
</style>

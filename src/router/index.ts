import { createRouter, createWebHashHistory } from 'vue-router';
import Home from '../views/Home.vue';
import Note from '../views/Note.vue';
import Check from '../views/Check.vue';
import UserCheck from '../views/UserCheck.vue';
import Batch from '../views/Batch.vue';
import Monitor from '../views/Monitor.vue';
import NoteMonitorData from '../views/NoteMonitorData.vue';
import Media from '../views/Media.vue';
import MediaDemo from '../views/MediaDemo.vue';

const routes = [
    {
        path: '/',
        name: 'Home',
        component: Home
    },
    {
        path: '/note',
        name: 'Note',
        component: Note
    },
    {
        path: '/check',
        name: 'Check',
        component: Check
    },
    {
        path: '/user_check',
        name: 'UserCheck',
        component: UserCheck
    },
    {
        path: '/batch',
        name: 'Batch',
        component: Batch
    },
    {
        path: '/monitor',
        name: 'Monitor',
        component: Monitor
    },
    {
        path: '/note-monitor/data/:taskId',
        name: 'NoteMonitorData',
        component: NoteMonitorData
    },
    {
        path: '/media',
        name: 'Media',
        component: Media
    },
    {
        path: '/media-demo',
        name: 'MediaDemo',
        component: MediaDemo
    },
];

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

export default router;
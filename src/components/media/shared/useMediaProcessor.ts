import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'
import { useMediaMainStore } from '@/stores/media-main'
import type { ProcessingOptions } from '@/stores/media-tasks'
import { useMediaEvents } from '@/composables/useMediaEvents'

export interface MediaProcessorConfig {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  defaultOptions?: ProcessingOptions
  maxFileSize?: number
  maxFileCount?: number
  supportedFormats?: string[]
}

/**
 * 通用媒体处理 Composable
 * 提供所有媒体处理组件的通用逻辑
 */
export function useMediaProcessor(config: MediaProcessorConfig) {
  const mediaStore = useMediaMainStore()
  const { mediaEventBus } = useMediaEvents()

  // ==================== 内置默认选项 ====================
  const builtInDefaults = {
    'video-convert': {
      outputFormat: 'mp3',
      quality: '192k',
      resizeEnabled: false,
      maxWidth: 1280
    },
    'audio-extract': {
      format: 'mp3',
      quality: 'high'
    },
    'asr': {
      language: 'zh',
      outputFormats: ['txt', 'srt'] as string[],
      audioQuality: 'high',
      enableTimestamps: true,
      filterSilence: false
    },
    'image-process': {
      outputFormat: 'jpeg',
      quality: '80',
      resizeEnabled: false,
      width: 1920,
      height: 1080,
      maintainAspectRatio: true,
      optimize: true,
      progressive: false,
      stripMetadata: true,
      colorSpace: 'srgb',
      saturation: 1.0
    }
  } as const

  // ==================== 响应式状态 ====================
  const fileList = ref<UploadFile[]>([])
  const outputDirectory = ref('')
  const isProcessing = ref(false)
  const filePathMap = ref<Map<string, string>>(new Map())
  
  // 直接从 store 获取数据，避免重复状态
  const currentTasks = computed(() => mediaStore.tasksStore.activeTasks)
  const taskResults = computed(() => Array.from(mediaStore.tasksStore.taskResults.values()))

  // 处理选项 - 使用内置默认值作为基础，然后合并传入的选项
  const processingOptions = reactive<ProcessingOptions>({
    ...builtInDefaults[config.taskType],
    ...(config.defaultOptions || {})
  })

  // ==================== 计算属性 ====================

  // 任务状态
  const taskStatus = computed(() => {
    if (isProcessing.value) return 'processing'
    if (currentTasks.value.some(t => t.status === 'completed')) return 'completed'
    if (currentTasks.value.some(t => t.status === 'error')) return 'error'
    return 'idle'
  })

  // 是否可以开始处理
  const canStartProcessing = computed(() => {
    return fileList.value.length > 0 &&
      outputDirectory.value &&
      !isProcessing.value
  })

  // 是否可以创建批量任务
  const canCreateBatch = computed(() => {
    return fileList.value.length > 1 &&
      outputDirectory.value &&
      !isProcessing.value
  })

  // 处理进度
  const overallProgress = computed(() => {
    if (currentTasks.value.length === 0) return 0
    const totalProgress = currentTasks.value.reduce((sum, task) => sum + task.progress, 0)
    return Math.floor(totalProgress / currentTasks.value.length)
  })

  // 成功的任务数
  const completedTasksCount = computed(() => {
    return currentTasks.value.filter(t => t.status === 'completed').length
  })

  // 失败的任务数
  const failedTasksCount = computed(() => {
    return currentTasks.value.filter(t => t.status === 'error').length
  })

  // ==================== 文件操作方法 ====================

  const handleFileAdded = (file: UploadFile, filePath: string) => {
    filePathMap.value.set(file.uid.toString(), filePath)

    // 如果没有设置输出目录，使用默认目录
    if (!outputDirectory.value) {
      outputDirectory.value = mediaStore.settingsStore.settings.defaultOutputDir
    }

    console.log(`[MediaProcessor] 文件已添加: ${file.name}`)
  }

  const handleFileRemoved = (file: UploadFile) => {
    filePathMap.value.delete(file.uid.toString())

    // 如果没有文件了，清理任务状态
    if (fileList.value.length === 0) {
      isProcessing.value = false
    }

    console.log(`[MediaProcessor] 文件已移除: ${file.name}`)
  }

  const handleError = (message: string) => {
    ElMessage.error(message)
    console.error(`[MediaProcessor] 错误: ${message}`)
  }

  // ==================== 任务操作方法 ====================

  const startSingleTask = async (fileIndex?: number) => {
    if (!canStartProcessing.value) {
      ElMessage.warning('请检查文件和输出目录设置')
      return
    }

    const targetFile = fileIndex !== undefined ? fileList.value[fileIndex] : fileList.value[0]
    if (!targetFile) {
      ElMessage.error('未找到要处理的文件')
      return
    }

    const filePath = filePathMap.value.get(targetFile.uid.toString())
    if (!filePath) {
      ElMessage.error('文件路径无效')
      return
    }

    try {
      isProcessing.value = true

      // 使用 store 的任务创建方法
      const taskId = await mediaStore.tasksStore.createSingleTask({
        type: config.taskType,
        fileName: targetFile.name,
        filePath: filePath,
        outputPath: outputDirectory.value,
        options: JSON.parse(JSON.stringify(processingOptions)),
        fileSize: targetFile.size
      })
      
      console.log(`[MediaProcessor] 任务已创建: ${taskId}`)

      ElMessage.success('任务已开始处理')

    } catch (error: any) {
      ElMessage.error(`开始任务失败: ${error.message}`)
      isProcessing.value = false
    }
  }


  const retryTask = async (task: any) => {
    try {
      // TODO: 实现重试逻辑
      ElMessage.success('任务重试已开始')
    } catch (error: any) {
      ElMessage.error(`重试失败: ${error.message}`)
    }
  }

  const pauseTask = async (task: any) => {
    try {
      // TODO: 实现暂停逻辑
      ElMessage.success('任务已暂停')
    } catch (error: any) {
      ElMessage.error(`暂停失败: ${error.message}`)
    }
  }

  const removeTask = async (task: any) => {
    try {
      await mediaStore.tasksStore.removeSingleTask(task.id)
      ElMessage.success('任务已删除')
    } catch (error: any) {
      ElMessage.error(`删除失败: ${error.message}`)
    }
  }

  // ==================== 选项处理方法 ====================

  const handleDirectorySelected = (directory: string) => {
    outputDirectory.value = directory
    console.log(`[MediaProcessor] 输出目录已设置: ${directory}`)
  }

  const handlePresetSaved = (presetName: string) => {
    ElMessage.success(`预设 "${presetName}" 保存成功`)
  }

  const updateProcessingOptions = (newOptions: Partial<ProcessingOptions>) => {
    Object.assign(processingOptions, newOptions)
  }

  // ==================== 工具方法 ====================

  const clearAll = async () => {
    fileList.value = []
    isProcessing.value = false
    filePathMap.value.clear()
    
    // 清空store中的数据
    try {
      await mediaStore.tasksStore.clearAllTasks()
      console.log('[MediaProcessor] 已清空所有数据')
    } catch (error) {
      console.error('[MediaProcessor] 清空数据失败:', error)
    }
  }

  const getTaskTypeName = (): string => {
    const names = {
      'video-convert': '视频转换',
      'audio-extract': '音频提取',
      'asr': '语音识别',
      'image-process': '图片处理'
    }
    return names[config.taskType] || config.taskType
  }

  // ==================== 处理状态监听 ====================
  
  // 监听任务状态变化，更新本地处理状态
  const setupProcessingStatusWatcher = () => {
    // 监听活动任务的变化来更新处理状态
    const stopWatcher = watch(
      () => currentTasks.value.length,
      (activeCount) => {
        // 当有活动任务时，标记为处理中
        if (activeCount > 0) {
          isProcessing.value = true
        } else {
          // 当没有活动任务时，检查是否应该停止处理状态
          setTimeout(() => {
            if (currentTasks.value.length === 0) {
              isProcessing.value = false
              console.log('[MediaProcessor] 所有任务已完成，停止处理状态')
            }
          }, 1000) // 延迟1秒检查，避免快速状态切换
        }
      },
      { immediate: true }
    )
    
    return stopWatcher
  }

  // 监听任务完成事件，自动清理文件列表
  const setupTaskCompletionWatcher = () => {
    const handleTaskResultAdded = ({ taskId, result }: { taskId: string; result: any }) => {
      console.log(`[MediaProcessor] 收到任务完成事件: ${taskId}`, result)
      
      // 根据任务结果找到对应的文件
      const fileToRemove = fileList.value.find(file => {
        // 精确匹配文件名
        return result.fileName === file.name
      })
      
      if (fileToRemove) {
        console.log(`[MediaProcessor] 任务完成，自动移除文件: ${fileToRemove.name}`)
        
        // 从文件列表中移除文件
        const fileIndex = fileList.value.findIndex(f => f.uid === fileToRemove.uid)
        if (fileIndex > -1) {
          fileList.value.splice(fileIndex, 1)
          filePathMap.value.delete(fileToRemove.uid.toString())
          
          ElMessage.success(`文件 ${fileToRemove.name} 处理完成已自动移除`)
        }
      } else {
        console.warn(`[MediaProcessor] 未找到对应的文件: ${result.fileName}`)
        console.log(`[MediaProcessor] 当前文件列表:`, fileList.value.map(f => f.name))
      }
      
      // 检查是否所有文件都已处理完成
      if (fileList.value.length === 0) {
        console.log('[MediaProcessor] 所有文件已处理完成并清理')
      }
    }
    
    // 监听任务结果添加事件
    mediaEventBus.on('task:result-added', handleTaskResultAdded)
    
    // 返回清理函数
    return () => {
      mediaEventBus.off('task:result-added', handleTaskResultAdded)
      console.log('[MediaProcessor] 已清理任务完成监听器')
    }
  }

  // ==================== 生命周期 ====================

  let statusWatcher: (() => void) | null = null
  let completionWatcher: (() => void) | null = null

  onMounted(async () => {
    if (!mediaStore.isInitialized) {
      await mediaStore.initialize()
    }
    
    // 设置处理状态监听器
    statusWatcher = setupProcessingStatusWatcher()
    // 设置任务完成监听器
    completionWatcher = setupTaskCompletionWatcher()
    
    console.log('[MediaProcessor] 组件已挂载，开始监听任务状态和完成状态')
  })

  // 清理监听器
  onUnmounted(() => {
    if (statusWatcher) {
      statusWatcher()
    }
    if (completionWatcher) {
      completionWatcher()
    }
    console.log('[MediaProcessor] 组件已卸载，清理监听器')
  })

  // ==================== 返回接口 ====================

  return {
    // 响应式状态
    fileList,
    outputDirectory,
    isProcessing,
    filePathMap,
    currentTasks,
    taskResults,
    processingOptions,

    // 计算属性
    taskStatus,
    canStartProcessing,
    canCreateBatch,
    overallProgress,
    completedTasksCount,
    failedTasksCount,

    // 文件操作方法
    handleFileAdded,
    handleFileRemoved,
    handleError,

    // 任务操作方法
    startSingleTask,
    retryTask,
    pauseTask,
    removeTask,

    // 选项处理方法
    handleDirectorySelected,
    handlePresetSaved,
    updateProcessingOptions,

    // 工具方法
    clearAll,
    getTaskTypeName,

    // Store 引用
    mediaStore
  }
}
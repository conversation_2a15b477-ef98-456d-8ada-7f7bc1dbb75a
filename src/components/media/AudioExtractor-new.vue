<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="audio-extract"
    title="音频提取"
    icon="mdi:music-box-outline"
    :uploader-props="{
      maxFileSize: 1024,
      uploadText: '拖拽视频文件到此处或点击上传',
      hint: '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB'
    }"
    :default-options="{
      outputFormat: 'mp3',
      audioQuality: '192k'
    }"
  >
    <template #content>
      <!-- 音频提取特有的内容 -->
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()

defineExpose({
  processor
})
</script>
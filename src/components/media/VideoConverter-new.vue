<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="video-convert"
    title="音视频转换"
    icon="mdi:video-outline"
    :uploader-props="{
      maxFileSize: 1024,
      uploadText: '拖拽视频文件到此处或点击上传',
      hint: '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB'
    }"
    :default-options="{
      outputFormat: 'mp3',
      quality: '192k',
      resizeEnabled: false,
      maxWidth: 1280
    }"
  >
    <template #content>
      <!-- 视频转换特有的内容（如果需要） -->
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()

// 视频转换特有的逻辑（如果有）

// 暴露给父组件
defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
// 视频转换特有的样式（如果需要）
</style>
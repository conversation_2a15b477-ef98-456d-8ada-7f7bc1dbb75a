<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="asr"
    title="语音识别"
    icon="mdi:microphone-outline"
    options-title="识别设置"
    :uploader-props="{
      maxFileSize: 500,
      uploadText: '拖拽音频/视频文件到此处或点击上传',
      hint: '支持音频：MP3, WAV, M4A, AAC, FLAC, OGG；视频：MP4, AVI, MOV 等，最大 500MB'
    }"
    :default-options="{
      language: 'zh-CN',
      outputFormats: ['txt', 'srt'],
      enableTimestamps: true,
      filterSilence: true
    }"
  >
    <template #extra-options>
      <!-- ASR 特有的选项 -->
      <div class="asr-extra-options">
        <el-form-item label="识别语言">
          <el-select v-model="language" placeholder="选择语言">
            <el-option label="中文" value="zh-CN" />
            <el-option label="英文" value="en-US" />
            <el-option label="日文" value="ja-JP" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="输出格式">
          <el-checkbox-group v-model="outputFormats">
            <el-checkbox label="txt">文本文件</el-checkbox>
            <el-checkbox label="srt">字幕文件</el-checkbox>
            <el-checkbox label="json">JSON格式</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
    </template>
    
    <template #content>
      <!-- ASR 结果预览 -->
      <div v-if="lastResult" class="asr-result-preview">
        <el-divider>识别结果预览</el-divider>
        <div class="result-text">
          {{ lastResult.text }}
        </div>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// ASR 特有的选项
const language = ref('zh-CN')
const outputFormats = ref(['txt', 'srt'])

// 获取最后一个完成的任务结果
const lastResult = computed(() => {
  const results = Array.from(tasksStore.taskResults.values())
    .filter(r => r.taskType === 'asr' && r.success)
    .sort((a, b) => b.completedAt - a.completedAt)
  return results[0]?.data
})

// 同步选项到处理器
watch([language, outputFormats], () => {
  if (processor.value) {
    const options = processor.value.processingOptions
    options.language = language.value
    options.outputFormats = outputFormats.value
  }
})

defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.asr-extra-options {
  margin-top: 16px;
}

.asr-result-preview {
  margin-top: 20px;
  
  .result-text {
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.6;
  }
}
</style>
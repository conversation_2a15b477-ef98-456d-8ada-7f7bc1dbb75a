<template>
  <div class="media-processor">
    <div class="section-header">
      <h3>
        <Icon :icon="icon" class="section-icon" />
        {{ title }}
      </h3>
      <slot name="header-actions" />
    </div>

    <FileUploader v-model="fileList" :task-type="taskType" v-bind="uploaderProps" @file-added="handleFileAdded"
      @file-removed="handleFileRemoved" @error="handleError" />

    <div class="options-section" v-if="fileList.length > 0">
      <el-divider>{{ optionsTitle }}</el-divider>
      <ProcessingOptions v-model="processingOptions" :task-type="taskType" v-bind="optionsProps" />
      <slot name="extra-options" />
    </div>

    <OutputDirectorySelector v-if="fileList.length > 0" v-model="outputDirectory" v-bind="directoryProps" />

    <TaskControls v-if="fileList.length > 0" :status="taskStatus" v-bind="controlsProps" @start="startProcessing"
      @pause="pauseProcessing" @stop="stopProcessing" />

    <slot name="content" />

    <TaskProgress v-if="activeTasks.length > 0" :tasks="activeTasks" v-bind="progressProps" />

    <TaskResultPanel v-if="results.length > 0" :results="results" v-bind="resultProps" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useMediaProcessor } from './shared/useMediaProcessor-new'
import { useMediaEvents } from '@/composables/useMediaEvents'
import FileUploader from './shared/FileUploader.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'
import OutputDirectorySelector from './shared/OutputDirectorySelector.vue'
import TaskControls from './shared/TaskControls.vue'
import TaskProgress from './shared/TaskProgress.vue'
import TaskResultPanel from './shared/TaskResultPanel.vue'

// Props定义
interface Props {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  title: string
  icon: string
  optionsTitle?: string
  uploaderProps?: Record<string, any>
  optionsProps?: Record<string, any>
  directoryProps?: Record<string, any>
  controlsProps?: Record<string, any>
  progressProps?: Record<string, any>
  resultProps?: Record<string, any>
  defaultOptions?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  optionsTitle: '处理设置'
})

// 使用组合式函数
const processor = useMediaProcessor({
  taskType: props.taskType,
  defaultOptions: props.defaultOptions || {},
  maxFileSize: props.uploaderProps?.maxFileSize,
  maxFileCount: props.uploaderProps?.maxFileCount,
  supportedFormats: props.uploaderProps?.supportedFormats
})

const { mediaEventBus } = useMediaEvents()

const {
  fileList,
  processingOptions,
  outputDirectory,
  activeTasks,
  results,
  startProcessing,
  pauseProcessing,
  stopProcessing,
  updateTaskProgress,
  updateTaskStatus,
  handleFileAdded,
  handleFileRemoved,
  handleError
} = processor

// 计算任务状态
const taskStatus = computed(() => {
  if (activeTasks.value.length === 0) return 'idle'
  if (activeTasks.value.some(t => t.status === 'processing')) return 'processing'
  if (activeTasks.value.some(t => t.status === 'paused')) return 'paused'
  return 'idle'
})

// 监听内部事件更新任务状态（避免直接监听 Electron 事件）
mediaEventBus.on('task:progress-changed', ({ taskId, progress, currentStep }) => {
  updateTaskProgress(taskId, progress, currentStep)
})

mediaEventBus.on('task:status-changed', ({ taskId, status, error }) => {
  updateTaskStatus(taskId, status, error)
})

mediaEventBus.on('task:result-added', ({ taskId, result }) => {
  console.log('任务已完成:', taskId, result)
})

// 暴露给父组件的数据
defineExpose({
  ...processor
})
</script>

<style lang="scss" scoped>
@use './shared/_media-common.scss' as *;

.media-processor {
  @include media-processor-layout;
  padding: 20px;
}
</style>
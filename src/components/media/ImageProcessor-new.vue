<template>
  <BaseMediaProcessor
    ref="processor"
    task-type="image-process"
    title="图片处理"
    icon="mdi:image-edit-outline"
    options-title="处理设置"
    :uploader-props="{
      maxFileSize: 50,
      maxFileCount: 50,
      uploadText: '拖拽图片文件到此处或点击上传',
      hint: '支持 JPG, PNG, WEBP, BMP, GIF 格式，单个文件最大 50MB'
    }"
    :default-options="{
      outputFormat: 'webp',
      quality: 85,
      resizeEnabled: false,
      maxWidth: 1920,
      maintainAspectRatio: true,
      optimize: true,
      stripMetadata: true
    }"
  >
    <template #extra-options>
      <!-- 图片处理特有的选项 -->
      <div class="image-extra-options">
        <el-form-item label="调整尺寸">
          <el-switch v-model="resizeEnabled" />
        </el-form-item>
        
        <el-form-item label="最大宽度" v-if="resizeEnabled">
          <el-input-number 
            v-model="maxWidth" 
            :min="100" 
            :max="4096" 
            :step="100"
          />
          <span class="unit">像素</span>
        </el-form-item>
        
        <el-form-item label="图片优化">
          <el-checkbox v-model="optimize">自动优化文件大小</el-checkbox>
          <el-checkbox v-model="stripMetadata">移除元数据</el-checkbox>
        </el-form-item>
      </div>
    </template>
    
    <template #content>
      <!-- 图片预览网格 -->
      <div v-if="fileList.length > 0" class="image-preview-grid">
        <el-divider>图片预览</el-divider>
        <div class="preview-container">
          <div 
            v-for="file in fileList.slice(0, 8)" 
            :key="file.uid"
            class="preview-item"
          >
            <el-image
              :src="file.url"
              :preview-src-list="[file.url]"
              fit="cover"
            />
            <div class="preview-info">
              {{ file.name }}
            </div>
          </div>
          <div v-if="fileList.length > 8" class="preview-more">
            +{{ fileList.length - 8 }} 更多
          </div>
        </div>
      </div>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseMediaProcessor from './BaseMediaProcessor.vue'
import { useMediaTasksStore } from '@/stores'

const processor = ref<InstanceType<typeof BaseMediaProcessor>>()
const tasksStore = useMediaTasksStore()

// 图片处理特有的选项
const resizeEnabled = ref(false)
const maxWidth = ref(1920)
const optimize = ref(true)
const stripMetadata = ref(true)

// 获取待处理文件列表
const fileList = computed(() => {
  return tasksStore.getPendingFiles('image-process')
})

// 同步选项到处理器
watch([resizeEnabled, maxWidth, optimize, stripMetadata], () => {
  if (processor.value) {
    const options = processor.value.processingOptions
    options.resizeEnabled = resizeEnabled.value
    options.maxWidth = maxWidth.value
    options.optimize = optimize.value
    options.stripMetadata = stripMetadata.value
  }
})

defineExpose({
  processor
})
</script>

<style lang="scss" scoped>
.image-extra-options {
  margin-top: 16px;
  
  .unit {
    margin-left: 8px;
    color: #909399;
  }
}

.image-preview-grid {
  margin-top: 20px;
  
  .preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    
    .preview-item {
      position: relative;
      
      .el-image {
        width: 100%;
        height: 120px;
        border-radius: 4px;
        overflow: hidden;
      }
      
      .preview-info {
        margin-top: 4px;
        font-size: 12px;
        color: #606266;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    
    .preview-more {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 120px;
      background: #f5f7fa;
      border-radius: 4px;
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>